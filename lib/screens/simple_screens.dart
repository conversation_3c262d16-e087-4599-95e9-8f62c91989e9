import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../utils/temp_localizations.dart';

// Quiz Generation Screen
class QuizGenerationScreen extends ConsumerWidget {
  final String pdfPath;
  final String title;
  final String description;
  final int questionCount;

  const QuizGenerationScreen({
    super.key,
    required this.pdfPath,
    required this.title,
    required this.description,
    required this.questionCount,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(TempLocalizations.generatingQuiz),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: AppConfig.paddingL),
            Text(
              TempLocalizations.generatingQuiz,
              style: theme.textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConfig.paddingM),
            Text(
              'Creating $questionCount questions from "$title"',
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Quiz Screen
class QuizScreen extends ConsumerWidget {
  final String quizId;

  const QuizScreen({super.key, required this.quizId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Sample Quiz'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => context.pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Progress
            LinearProgressIndicator(
              value: 0.3,
              backgroundColor: theme.colorScheme.surfaceVariant,
            ),
            const SizedBox(height: AppConfig.paddingL),
            
            // Question
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConfig.paddingL),
                child: Text(
                  'Sample Question: What is the main topic of this document?',
                  style: theme.textTheme.titleLarge,
                ),
              ),
            ),
            const SizedBox(height: AppConfig.paddingL),
            
            // Options
            ...List.generate(4, (index) {
              final options = ['Option A', 'Option B', 'Option C', 'Option D'];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConfig.paddingM),
                child: Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      child: Text(String.fromCharCode(65 + index)),
                    ),
                    title: Text(options[index]),
                    onTap: () {},
                  ),
                ),
              );
            }),
            
            const Spacer(),
            
            ElevatedButton(
              onPressed: () => context.push('/results/sample'),
              child: Text(TempLocalizations.nextQuestion),
            ),
          ],
        ),
      ),
    );
  }
}

// Results Screen
class ResultsScreen extends ConsumerWidget {
  final String resultId;

  const ResultsScreen({super.key, required this.resultId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(TempLocalizations.results),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {},
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        child: Column(
          children: [
            // Score Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConfig.paddingL),
                child: Column(
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppConfig.successColor,
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '85%',
                              style: theme.textTheme.headlineMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'A',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: AppConfig.paddingL),
                    Text(
                      TempLocalizations.excellent,
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: AppConfig.successColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppConfig.paddingL),
            
            // Stats
            Row(
              children: [
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(AppConfig.paddingM),
                      child: Column(
                        children: [
                          Text(
                            '8',
                            style: theme.textTheme.headlineSmall?.copyWith(
                              color: AppConfig.successColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(TempLocalizations.correctAnswers),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(AppConfig.paddingM),
                      child: Column(
                        children: [
                          Text(
                            '2',
                            style: theme.textTheme.headlineSmall?.copyWith(
                              color: AppConfig.errorColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(TempLocalizations.incorrectAnswers),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const Spacer(),
            
            Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => context.push('/quiz/sample'),
                    child: Text(TempLocalizations.retakeQuiz),
                  ),
                ),
                const SizedBox(height: AppConfig.paddingS),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () => context.go('/'),
                    child: const Text('Go to Home'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// History Screen
class HistoryScreen extends ConsumerWidget {
  const HistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(TempLocalizations.history),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        child: Column(
          children: [
            // Empty State
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.history,
                      size: 80,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(height: AppConfig.paddingL),
                    Text(
                      TempLocalizations.noQuizzesYet,
                      style: theme.textTheme.titleLarge,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppConfig.paddingS),
                    Text(
                      TempLocalizations.createFirstQuiz,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppConfig.paddingL),
                    ElevatedButton(
                      onPressed: () => context.push('/upload'),
                      child: Text(TempLocalizations.createQuiz),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.push('/upload'),
        child: const Icon(Icons.add),
      ),
    );
  }
}
