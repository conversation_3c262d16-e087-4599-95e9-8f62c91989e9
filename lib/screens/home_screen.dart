import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../utils/temp_localizations.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(TempLocalizations.appName),
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => context.push('/settings'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Welcome Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConfig.paddingL),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome to ${TempLocalizations.appName}',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppConfig.paddingS),
                    Text(
                      TempLocalizations.appDescription,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: AppConfig.paddingL),
            
            // Quick Actions
            Text(
              'Quick Actions',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),
            
            Row(
              children: [
                Expanded(
                  child: Card(
                    child: InkWell(
                      onTap: () => context.push('/upload'),
                      borderRadius: BorderRadius.circular(AppConfig.radiusM),
                      child: Padding(
                        padding: const EdgeInsets.all(AppConfig.paddingL),
                        child: Column(
                          children: [
                            Icon(
                              Icons.upload_file,
                              size: 48,
                              color: colorScheme.primary,
                            ),
                            const SizedBox(height: AppConfig.paddingS),
                            Text(
                              TempLocalizations.uploadPdf,
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: AppConfig.paddingXS),
                            Text(
                              'Create new quiz',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: AppConfig.paddingM),
                Expanded(
                  child: Card(
                    child: InkWell(
                      onTap: () => context.push('/history'),
                      borderRadius: BorderRadius.circular(AppConfig.radiusM),
                      child: Padding(
                        padding: const EdgeInsets.all(AppConfig.paddingL),
                        child: Column(
                          children: [
                            Icon(
                              Icons.history,
                              size: 48,
                              color: colorScheme.secondary,
                            ),
                            const SizedBox(height: AppConfig.paddingS),
                            Text(
                              TempLocalizations.history,
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: AppConfig.paddingXS),
                            Text(
                              'View past quizzes',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConfig.paddingXL),

            // Get Started Message
            Card(
              child: Padding(
                padding: const EdgeInsets.all(AppConfig.paddingL),
                child: Column(
                  children: [
                    Icon(
                      Icons.quiz_outlined,
                      size: 64,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(height: AppConfig.paddingM),
                    Text(
                      TempLocalizations.noQuizzesYet,
                      style: theme.textTheme.titleMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppConfig.paddingS),
                    Text(
                      TempLocalizations.createFirstQuiz,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.push('/upload'),
        icon: const Icon(Icons.add),
        label: Text(TempLocalizations.createQuiz),
      ),
    );
  }
}
