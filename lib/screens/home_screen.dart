import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../models/quiz_model.dart';
import '../services/quiz_service.dart';
import '../flutter_gen/gen_l10n/app_localizations.dart';

// Provider for recent quizzes
final recentQuizzesProvider = FutureProvider<List<Quiz>>((ref) async {
  final quizService = QuizService();
  final allQuizzes = await quizService.getAllQuizzes();
  return allQuizzes.take(5).toList();
});

// Provider for user statistics
final userStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final quizService = QuizService();
  return await quizService.getUserStatistics();
});

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                l10n.appName,
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      colorScheme.primary,
                      colorScheme.secondary,
                    ],
                  ),
                ),
                child: const Center(
                  child: Icon(
                    Icons.quiz,
                    size: 80,
                    color: Colors.white70,
                  ),
                ),
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.settings),
                onPressed: () => context.push('/settings'),
              ),
            ],
          ),

          // Main Content
          SliverPadding(
            padding: const EdgeInsets.all(AppConfig.paddingM),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                // Welcome Section
                _buildWelcomeSection(context, l10n, theme),
                const SizedBox(height: AppConfig.paddingL),

                // Quick Actions
                _buildQuickActions(context, l10n, theme),
                const SizedBox(height: AppConfig.paddingL),

                // Statistics
                _buildStatistics(context, l10n, ref),
                const SizedBox(height: AppConfig.paddingL),

                // Recent Quizzes
                _buildRecentQuizzes(context, l10n, ref),
              ]),
            ),
          ),
        ],
      ),
      
      // Bottom Navigation
      bottomNavigationBar: _buildBottomNavigation(context, l10n),
      
      // Floating Action Button
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.push('/upload'),
        icon: const Icon(Icons.add),
        label: Text(l10n.createQuiz),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context, AppLocalizations l10n, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome to ${l10n.appName}',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.paddingS),
            Text(
              l10n.appDescription,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, AppLocalizations l10n, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConfig.paddingM),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                context: context,
                icon: Icons.upload_file,
                title: l10n.uploadPdf,
                subtitle: 'Create new quiz',
                onTap: () => context.push('/upload'),
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(width: AppConfig.paddingM),
            Expanded(
              child: _buildActionCard(
                context: context,
                icon: Icons.history,
                title: l10n.history,
                subtitle: 'View past quizzes',
                onTap: () => context.push('/history'),
                color: theme.colorScheme.secondary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color color,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConfig.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.paddingM),
          child: Column(
            children: [
              Icon(
                icon,
                size: 40,
                color: color,
              ),
              const SizedBox(height: AppConfig.paddingS),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConfig.paddingXS),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatistics(BuildContext context, AppLocalizations l10n, WidgetRef ref) {
    final statsAsync = ref.watch(userStatsProvider);
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.statistics,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConfig.paddingM),
        statsAsync.when(
          data: (stats) => Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context: context,
                  title: l10n.totalQuizzes,
                  value: '${stats['totalQuizzes'] ?? 0}',
                  icon: Icons.quiz,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(width: AppConfig.paddingM),
              Expanded(
                child: _buildStatCard(
                  context: context,
                  title: l10n.completedQuizzes,
                  value: '${stats['completedQuizzes'] ?? 0}',
                  icon: Icons.check_circle,
                  color: theme.colorScheme.secondary,
                ),
              ),
              const SizedBox(width: AppConfig.paddingM),
              Expanded(
                child: _buildStatCard(
                  context: context,
                  title: l10n.averageScore,
                  value: '${(stats['averageScore'] ?? 0.0).toStringAsFixed(1)}%',
                  icon: Icons.trending_up,
                  color: AppConfig.successColor,
                ),
              ),
            ],
          ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Text('Error: $error'),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required BuildContext context,
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: AppConfig.paddingS),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: AppConfig.paddingXS),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentQuizzes(BuildContext context, AppLocalizations l10n, WidgetRef ref) {
    final quizzesAsync = ref.watch(recentQuizzesProvider);
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              l10n.recentQuizzes,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => context.push('/history'),
              child: Text(l10n.history),
            ),
          ],
        ),
        const SizedBox(height: AppConfig.paddingM),
        quizzesAsync.when(
          data: (quizzes) {
            if (quizzes.isEmpty) {
              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConfig.paddingL),
                  child: Column(
                    children: [
                      Icon(
                        Icons.quiz_outlined,
                        size: 64,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(height: AppConfig.paddingM),
                      Text(
                        l10n.noQuizzesYet,
                        style: theme.textTheme.titleMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppConfig.paddingS),
                      Text(
                        l10n.createFirstQuiz,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            }

            return Column(
              children: quizzes.map((quiz) => _buildQuizCard(context, quiz, l10n)).toList(),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Text('Error: $error'),
        ),
      ],
    );
  }

  Widget _buildQuizCard(BuildContext context, Quiz quiz, AppLocalizations l10n) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: AppConfig.paddingS),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: theme.colorScheme.primaryContainer,
          child: Icon(
            Icons.quiz,
            color: theme.colorScheme.onPrimaryContainer,
          ),
        ),
        title: Text(
          quiz.title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          '${quiz.questions.length} questions • ${quiz.status.name}',
          style: theme.textTheme.bodySmall,
        ),
        trailing: quiz.status == QuizStatus.ready
            ? IconButton(
                icon: const Icon(Icons.play_arrow),
                onPressed: () => context.push('/quiz/${quiz.id}'),
              )
            : null,
        onTap: () => context.push('/quiz/${quiz.id}'),
      ),
    );
  }

  Widget _buildBottomNavigation(BuildContext context, AppLocalizations l10n) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: 0,
      items: [
        BottomNavigationBarItem(
          icon: const Icon(Icons.home),
          label: l10n.home,
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.quiz),
          label: l10n.quizzes,
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.history),
          label: l10n.history,
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.settings),
          label: l10n.settings,
        ),
      ],
      onTap: (index) {
        switch (index) {
          case 0:
            // Already on home
            break;
          case 1:
            context.push('/history');
            break;
          case 2:
            context.push('/history');
            break;
          case 3:
            context.push('/settings');
            break;
        }
      },
    );
  }
}
