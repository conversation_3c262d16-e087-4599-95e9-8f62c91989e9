import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../config/app_config.dart';
import '../app.dart';
import '../services/storage_service.dart';
import '../services/gemini_service.dart';
import '../flutter_gen/gen_l10n/app_localizations.dart';

// Provider for API key
final apiKeyProvider = StateProvider<String>((ref) => '');

// Provider for settings
final settingsProvider = StateNotifierProvider<SettingsNotifier, Map<String, dynamic>>(
  (ref) => SettingsNotifier(),
);

class SettingsNotifier extends StateNotifier<Map<String, dynamic>> {
  final StorageService _storageService = StorageService();

  SettingsNotifier() : super({}) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = {
        'apiKey': _storageService.getString('gemini_api_key') ?? '',
        'language': _storageService.getString('language') ?? 'en',
        'theme': _storageService.getString('theme') ?? 'system',
        'notifications': _storageService.getBool('notifications') ?? true,
        'autoSave': _storageService.getBool('auto_save') ?? true,
      };
      state = settings;
    } catch (e) {
      // Handle error
    }
  }

  Future<void> updateSetting(String key, dynamic value) async {
    try {
      state = {...state, key: value};
      
      switch (value.runtimeType) {
        case String:
          await _storageService.setString(key, value as String);
          break;
        case bool:
          await _storageService.setBool(key, value as bool);
          break;
        case int:
          await _storageService.setInt(key, value as int);
          break;
        case double:
          await _storageService.setDouble(key, value as double);
          break;
      }
    } catch (e) {
      // Handle error
    }
  }
}

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  final _apiKeyController = TextEditingController();
  bool _isTestingConnection = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final settings = ref.read(settingsProvider);
      _apiKeyController.text = settings['apiKey'] ?? '';
    });
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final settings = ref.watch(settingsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.settings),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConfig.paddingM),
        children: [
          // API Configuration Section
          _buildSectionHeader(context, l10n.apiConfiguration, Icons.api),
          _buildApiConfigSection(context, l10n, theme, settings),
          const SizedBox(height: AppConfig.paddingL),

          // Appearance Section
          _buildSectionHeader(context, 'Appearance', Icons.palette),
          _buildAppearanceSection(context, l10n, theme, settings, ref),
          const SizedBox(height: AppConfig.paddingL),

          // Preferences Section
          _buildSectionHeader(context, 'Preferences', Icons.tune),
          _buildPreferencesSection(context, l10n, theme, settings, ref),
          const SizedBox(height: AppConfig.paddingL),

          // Data Management Section
          _buildSectionHeader(context, 'Data Management', Icons.storage),
          _buildDataManagementSection(context, l10n, theme),
          const SizedBox(height: AppConfig.paddingL),

          // About Section
          _buildSectionHeader(context, l10n.about, Icons.info),
          _buildAboutSection(context, l10n, theme),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, IconData icon) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConfig.paddingM),
      child: Row(
        children: [
          Icon(
            icon,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: AppConfig.paddingS),
          Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApiConfigSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    Map<String, dynamic> settings,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.geminiApiKey,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppConfig.paddingS),
            Text(
              l10n.apiKeyDescription,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppConfig.paddingM),
            
            TextFormField(
              controller: _apiKeyController,
              decoration: InputDecoration(
                labelText: l10n.geminiApiKey,
                hintText: l10n.apiKeyPlaceholder,
                suffixIcon: IconButton(
                  icon: const Icon(Icons.visibility),
                  onPressed: () {
                    // Toggle password visibility
                  },
                ),
              ),
              obscureText: true,
              onChanged: (value) {
                ref.read(settingsProvider.notifier).updateSetting('apiKey', value);
              },
            ),
            const SizedBox(height: AppConfig.paddingM),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isTestingConnection ? null : _testConnection,
                    child: _isTestingConnection
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(l10n.testConnection),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppearanceSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    Map<String, dynamic> settings,
    WidgetRef ref,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          children: [
            // Language Setting
            ListTile(
              leading: const Icon(Icons.language),
              title: Text(l10n.language),
              subtitle: Text(_getLanguageName(settings['language'] ?? 'en')),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showLanguageDialog(context, l10n, ref),
            ),
            const Divider(),
            
            // Theme Setting
            ListTile(
              leading: const Icon(Icons.brightness_6),
              title: Text(l10n.theme),
              subtitle: Text(_getThemeName(settings['theme'] ?? 'system')),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showThemeDialog(context, l10n, ref),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferencesSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
    Map<String, dynamic> settings,
    WidgetRef ref,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          children: [
            SwitchListTile(
              secondary: const Icon(Icons.notifications),
              title: const Text('Notifications'),
              subtitle: const Text('Receive notifications for quiz reminders'),
              value: settings['notifications'] ?? true,
              onChanged: (value) {
                ref.read(settingsProvider.notifier).updateSetting('notifications', value);
              },
            ),
            const Divider(),
            
            SwitchListTile(
              secondary: const Icon(Icons.save),
              title: const Text('Auto Save'),
              subtitle: const Text('Automatically save quiz progress'),
              value: settings['autoSave'] ?? true,
              onChanged: (value) {
                ref.read(settingsProvider.notifier).updateSetting('autoSave', value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataManagementSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          children: [
            ListTile(
              leading: const Icon(Icons.backup),
              title: Text(l10n.backup),
              subtitle: const Text('Backup your quizzes and results'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showBackupDialog(context, l10n),
            ),
            const Divider(),
            
            ListTile(
              leading: const Icon(Icons.restore),
              title: Text(l10n.restore),
              subtitle: const Text('Restore from backup'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showRestoreDialog(context, l10n),
            ),
            const Divider(),
            
            ListTile(
              leading: const Icon(Icons.delete_forever, color: AppConfig.errorColor),
              title: const Text('Clear All Data', style: TextStyle(color: AppConfig.errorColor)),
              subtitle: const Text('Delete all quizzes and results'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showClearDataDialog(context, l10n),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection(
    BuildContext context,
    AppLocalizations l10n,
    ThemeData theme,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.paddingL),
        child: Column(
          children: [
            ListTile(
              leading: const Icon(Icons.info),
              title: Text(l10n.version),
              subtitle: Text(AppConfig.appVersion),
            ),
            const Divider(),
            
            ListTile(
              leading: const Icon(Icons.person),
              title: Text(l10n.developer),
              subtitle: const Text('Augment Code'),
            ),
            const Divider(),
            
            ListTile(
              leading: const Icon(Icons.description),
              title: Text(l10n.licenses),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showLicensesDialog(context),
            ),
          ],
        ),
      ),
    );
  }

  String _getLanguageName(String code) {
    switch (code) {
      case 'ar':
        return 'العربية';
      case 'en':
      default:
        return 'English';
    }
  }

  String _getThemeName(String theme) {
    switch (theme) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'system':
      default:
        return 'System';
    }
  }

  Future<void> _testConnection() async {
    setState(() {
      _isTestingConnection = true;
    });

    try {
      final apiKey = _apiKeyController.text.trim();
      if (apiKey.isEmpty) {
        _showSnackBar('Please enter an API key first');
        return;
      }

      // Initialize Gemini service with the API key
      GeminiService().initialize(apiKey);
      
      // Test connection by making a simple request
      // This would be implemented in the actual service
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      _showSnackBar('Connection successful!', isSuccess: true);
    } catch (e) {
      _showSnackBar('Connection failed: ${e.toString()}');
    } finally {
      setState(() {
        _isTestingConnection = false;
      });
    }
  }

  void _showLanguageDialog(BuildContext context, AppLocalizations l10n, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.language),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: ref.read(settingsProvider)['language'],
              onChanged: (value) {
                if (value != null) {
                  ref.read(settingsProvider.notifier).updateSetting('language', value);
                  ref.read(localeProvider.notifier).state = Locale(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('العربية'),
              value: 'ar',
              groupValue: ref.read(settingsProvider)['language'],
              onChanged: (value) {
                if (value != null) {
                  ref.read(settingsProvider.notifier).updateSetting('language', value);
                  ref.read(localeProvider.notifier).state = Locale(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showThemeDialog(BuildContext context, AppLocalizations l10n, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.theme),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: Text(l10n.lightMode),
              value: 'light',
              groupValue: ref.read(settingsProvider)['theme'],
              onChanged: (value) {
                if (value != null) {
                  ref.read(settingsProvider.notifier).updateSetting('theme', value);
                  ref.read(themeProvider.notifier).state = ThemeMode.light;
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<String>(
              title: Text(l10n.darkMode),
              value: 'dark',
              groupValue: ref.read(settingsProvider)['theme'],
              onChanged: (value) {
                if (value != null) {
                  ref.read(settingsProvider.notifier).updateSetting('theme', value);
                  ref.read(themeProvider.notifier).state = ThemeMode.dark;
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<String>(
              title: Text(l10n.systemMode),
              value: 'system',
              groupValue: ref.read(settingsProvider)['theme'],
              onChanged: (value) {
                if (value != null) {
                  ref.read(settingsProvider.notifier).updateSetting('theme', value);
                  ref.read(themeProvider.notifier).state = ThemeMode.system;
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showBackupDialog(BuildContext context, AppLocalizations l10n) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.backup),
        content: const Text('Backup functionality would be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.close),
          ),
        ],
      ),
    );
  }

  void _showRestoreDialog(BuildContext context, AppLocalizations l10n) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.restore),
        content: const Text('Restore functionality would be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.close),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog(BuildContext context, AppLocalizations l10n) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text('Are you sure you want to delete all quizzes and results? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () {
              // Implement clear data functionality
              Navigator.of(context).pop();
              _showSnackBar('All data cleared successfully');
            },
            child: const Text('Clear', style: TextStyle(color: AppConfig.errorColor)),
          ),
        ],
      ),
    );
  }

  void _showLicensesDialog(BuildContext context) {
    showLicensePage(context: context);
  }

  void _showSnackBar(String message, {bool isSuccess = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isSuccess ? AppConfig.successColor : null,
      ),
    );
  }
}
