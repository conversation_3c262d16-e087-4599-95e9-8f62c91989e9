import 'dart:async';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quiz_model.dart';
import '../models/quiz_result_model.dart';
import '../config/app_config.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  Database? _database;
  SharedPreferences? _prefs;

  /// Initialize the storage service
  Future<void> initialize() async {
    await _initializeDatabase();
    await _initializePreferences();
  }

  /// Initialize SQLite database
  Future<void> _initializeDatabase() async {
    try {
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final path = join(documentsDirectory.path, AppConfig.databaseName);
      
      _database = await openDatabase(
        path,
        version: AppConfig.databaseVersion,
        onCreate: _createTables,
        onUpgrade: _upgradeTables,
      );
    } catch (e) {
      throw StorageException('Failed to initialize database: ${e.toString()}');
    }
  }

  /// Initialize SharedPreferences
  Future<void> _initializePreferences() async {
    try {
      _prefs = await SharedPreferences.getInstance();
    } catch (e) {
      throw StorageException('Failed to initialize preferences: ${e.toString()}');
    }
  }

  /// Create database tables
  Future<void> _createTables(Database db, int version) async {
    // Quizzes table
    await db.execute('''
      CREATE TABLE quizzes (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        sourceFileName TEXT NOT NULL,
        sourcePdfPath TEXT,
        questions TEXT NOT NULL,
        status TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER,
        timeLimit INTEGER NOT NULL DEFAULT 0,
        category TEXT,
        averageDifficulty TEXT
      )
    ''');

    // Quiz results table
    await db.execute('''
      CREATE TABLE quiz_results (
        id TEXT PRIMARY KEY,
        quizId TEXT NOT NULL,
        quizTitle TEXT NOT NULL,
        questionResults TEXT NOT NULL,
        status TEXT NOT NULL,
        startedAt INTEGER NOT NULL,
        completedAt INTEGER,
        totalTimeSeconds INTEGER NOT NULL,
        scorePercentage REAL NOT NULL,
        feedback TEXT,
        analytics TEXT,
        FOREIGN KEY (quizId) REFERENCES quizzes (id) ON DELETE CASCADE
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_quizzes_status ON quizzes (status)');
    await db.execute('CREATE INDEX idx_quizzes_created_at ON quizzes (createdAt)');
    await db.execute('CREATE INDEX idx_quiz_results_quiz_id ON quiz_results (quizId)');
    await db.execute('CREATE INDEX idx_quiz_results_completed_at ON quiz_results (completedAt)');
  }

  /// Upgrade database tables
  Future<void> _upgradeTables(Database db, int oldVersion, int newVersion) async {
    // Handle database migrations here when needed
    // For now, we'll just recreate tables
    if (oldVersion < newVersion) {
      await db.execute('DROP TABLE IF EXISTS quiz_results');
      await db.execute('DROP TABLE IF EXISTS quizzes');
      await _createTables(db, newVersion);
    }
  }

  /// Save a quiz to the database
  Future<void> saveQuiz(Quiz quiz) async {
    if (_database == null) throw StorageException('Database not initialized');
    
    try {
      await _database!.insert(
        'quizzes',
        quiz.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw StorageException('Failed to save quiz: ${e.toString()}');
    }
  }

  /// Get a quiz by ID
  Future<Quiz?> getQuiz(String id) async {
    if (_database == null) throw StorageException('Database not initialized');
    
    try {
      final List<Map<String, dynamic>> maps = await _database!.query(
        'quizzes',
        where: 'id = ?',
        whereArgs: [id],
      );
      
      if (maps.isNotEmpty) {
        return Quiz.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      throw StorageException('Failed to get quiz: ${e.toString()}');
    }
  }

  /// Get all quizzes
  Future<List<Quiz>> getAllQuizzes({QuizStatus? status}) async {
    if (_database == null) throw StorageException('Database not initialized');
    
    try {
      final List<Map<String, dynamic>> maps;
      
      if (status != null) {
        maps = await _database!.query(
          'quizzes',
          where: 'status = ?',
          whereArgs: [status.name],
          orderBy: 'createdAt DESC',
        );
      } else {
        maps = await _database!.query(
          'quizzes',
          orderBy: 'createdAt DESC',
        );
      }
      
      return maps.map((map) => Quiz.fromMap(map)).toList();
    } catch (e) {
      throw StorageException('Failed to get quizzes: ${e.toString()}');
    }
  }

  /// Update a quiz
  Future<void> updateQuiz(Quiz quiz) async {
    if (_database == null) throw StorageException('Database not initialized');
    
    try {
      final updatedQuiz = quiz.copyWith(updatedAt: DateTime.now());
      await _database!.update(
        'quizzes',
        updatedQuiz.toMap(),
        where: 'id = ?',
        whereArgs: [quiz.id],
      );
    } catch (e) {
      throw StorageException('Failed to update quiz: ${e.toString()}');
    }
  }

  /// Delete a quiz
  Future<void> deleteQuiz(String id) async {
    if (_database == null) throw StorageException('Database not initialized');
    
    try {
      await _database!.delete(
        'quizzes',
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      throw StorageException('Failed to delete quiz: ${e.toString()}');
    }
  }

  /// Save quiz result
  Future<void> saveQuizResult(QuizResult result) async {
    if (_database == null) throw StorageException('Database not initialized');
    
    try {
      await _database!.insert(
        'quiz_results',
        result.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw StorageException('Failed to save quiz result: ${e.toString()}');
    }
  }

  /// Get quiz results for a specific quiz
  Future<List<QuizResult>> getQuizResults(String quizId) async {
    if (_database == null) throw StorageException('Database not initialized');
    
    try {
      final List<Map<String, dynamic>> maps = await _database!.query(
        'quiz_results',
        where: 'quizId = ?',
        whereArgs: [quizId],
        orderBy: 'completedAt DESC',
      );
      
      return maps.map((map) => QuizResult.fromMap(map)).toList();
    } catch (e) {
      throw StorageException('Failed to get quiz results: ${e.toString()}');
    }
  }

  /// Get all quiz results
  Future<List<QuizResult>> getAllQuizResults() async {
    if (_database == null) throw StorageException('Database not initialized');
    
    try {
      final List<Map<String, dynamic>> maps = await _database!.query(
        'quiz_results',
        orderBy: 'completedAt DESC',
      );
      
      return maps.map((map) => QuizResult.fromMap(map)).toList();
    } catch (e) {
      throw StorageException('Failed to get all quiz results: ${e.toString()}');
    }
  }

  /// Get user statistics
  Future<Map<String, dynamic>> getUserStatistics() async {
    if (_database == null) throw StorageException('Database not initialized');
    
    try {
      final totalQuizzes = await _database!.rawQuery('SELECT COUNT(*) as count FROM quizzes');
      final completedQuizzes = await _database!.rawQuery(
        'SELECT COUNT(*) as count FROM quiz_results WHERE status = ?',
        [QuizResultStatus.completed.name],
      );
      final averageScore = await _database!.rawQuery(
        'SELECT AVG(scorePercentage) as average FROM quiz_results WHERE status = ?',
        [QuizResultStatus.completed.name],
      );
      
      return {
        'totalQuizzes': totalQuizzes.first['count'] ?? 0,
        'completedQuizzes': completedQuizzes.first['count'] ?? 0,
        'averageScore': averageScore.first['average'] ?? 0.0,
      };
    } catch (e) {
      throw StorageException('Failed to get user statistics: ${e.toString()}');
    }
  }

  /// Preferences methods
  Future<void> setString(String key, String value) async {
    if (_prefs == null) throw StorageException('Preferences not initialized');
    await _prefs!.setString(key, value);
  }

  String? getString(String key) {
    if (_prefs == null) throw StorageException('Preferences not initialized');
    return _prefs!.getString(key);
  }

  Future<void> setBool(String key, bool value) async {
    if (_prefs == null) throw StorageException('Preferences not initialized');
    await _prefs!.setBool(key, value);
  }

  bool? getBool(String key) {
    if (_prefs == null) throw StorageException('Preferences not initialized');
    return _prefs!.getBool(key);
  }

  Future<void> setInt(String key, int value) async {
    if (_prefs == null) throw StorageException('Preferences not initialized');
    await _prefs!.setInt(key, value);
  }

  int? getInt(String key) {
    if (_prefs == null) throw StorageException('Preferences not initialized');
    return _prefs!.getInt(key);
  }

  Future<void> setDouble(String key, double value) async {
    if (_prefs == null) throw StorageException('Preferences not initialized');
    await _prefs!.setDouble(key, value);
  }

  double? getDouble(String key) {
    if (_prefs == null) throw StorageException('Preferences not initialized');
    return _prefs!.getDouble(key);
  }

  Future<void> remove(String key) async {
    if (_prefs == null) throw StorageException('Preferences not initialized');
    await _prefs!.remove(key);
  }

  Future<void> clear() async {
    if (_prefs == null) throw StorageException('Preferences not initialized');
    await _prefs!.clear();
  }

  /// Clean up resources
  Future<void> dispose() async {
    await _database?.close();
    _database = null;
    _prefs = null;
  }
}

class StorageException implements Exception {
  final String message;
  
  StorageException(this.message);
  
  @override
  String toString() => 'StorageException: $message';
}
